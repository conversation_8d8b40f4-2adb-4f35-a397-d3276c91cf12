class CreateResolutionMotives < ActiveRecord::Migration[7.0]
  def change
    create_table :resolution_motives do |t|
      t.string :name, null: false
      t.text :description
      t.references :account, null: false, foreign_key: true
      t.boolean :active, default: true, null: false
      t.integer :position, default: 0

      t.timestamps
    end

    add_index :resolution_motives, [:account_id, :name], unique: true
    add_index :resolution_motives, [:account_id, :active]
    add_index :resolution_motives, [:account_id, :position]
  end
end
