import * as MutationHelpers from 'shared/helpers/vuex/mutationHelpers';
import types from '../mutation-types';
import ResolutionMotivesAPI from '../../api/resolutionMotives';

export const state = {
  records: [],
  uiFlags: {
    isFetching: false,
    isFetchingItem: false,
    isCreating: false,
    isUpdating: false,
    isDeleting: false,
  },
};

export const getters = {
  getResolutionMotives(_state) {
    return _state.records;
  },
  getUIFlags(_state) {
    return _state.uiFlags;
  },
  getActiveResolutionMotives(_state) {
    return _state.records
      .filter(record => record.active)
      .sort((a, b) => a.position - b.position || a.name.localeCompare(b.name));
  },
};

export const actions = {
  get: async function getResolutionMotives({ commit }) {
    commit(types.SET_RESOLUTION_MOTIVE_UI_FLAG, { isFetching: true });
    try {
      const response = await ResolutionMotivesAPI.get();
      const sortedMotives = response.data.payload.sort((a, b) =>
        a.position - b.position || a.name.localeCompare(b.name)
      );
      commit(types.SET_RESOLUTION_MOTIVES, sortedMotives);
    } catch (error) {
      // Ignore error
    } finally {
      commit(types.SET_RESOLUTION_MOTIVE_UI_FLAG, { isFetching: false });
    }
  },

  create: async function createResolutionMotive({ commit }, motiveObj) {
    commit(types.SET_RESOLUTION_MOTIVE_UI_FLAG, { isCreating: true });
    try {
      const response = await ResolutionMotivesAPI.create(motiveObj);
      commit(types.ADD_RESOLUTION_MOTIVE, response.data);
    } catch (error) {
      const errorMessage = error?.response?.data?.message;
      throw new Error(errorMessage);
    } finally {
      commit(types.SET_RESOLUTION_MOTIVE_UI_FLAG, { isCreating: false });
    }
  },

  update: async function updateResolutionMotive({ commit }, { id, ...updateObj }) {
    commit(types.SET_RESOLUTION_MOTIVE_UI_FLAG, { isUpdating: true });
    try {
      const response = await ResolutionMotivesAPI.update(id, updateObj);
      commit(types.EDIT_RESOLUTION_MOTIVE, response.data);
    } catch (error) {
      const errorMessage = error?.response?.data?.message;
      throw new Error(errorMessage);
    } finally {
      commit(types.SET_RESOLUTION_MOTIVE_UI_FLAG, { isUpdating: false });
    }
  },

  delete: async function deleteResolutionMotive({ commit }, id) {
    commit(types.SET_RESOLUTION_MOTIVE_UI_FLAG, { isDeleting: true });
    try {
      await ResolutionMotivesAPI.delete(id);
      commit(types.DELETE_RESOLUTION_MOTIVE, id);
    } catch (error) {
      const errorMessage = error?.response?.data?.error || error?.response?.data?.message;
      throw new Error(errorMessage);
    } finally {
      commit(types.SET_RESOLUTION_MOTIVE_UI_FLAG, { isDeleting: false });
    }
  },
};

export const mutations = {
  [types.SET_RESOLUTION_MOTIVE_UI_FLAG](_state, data) {
    _state.uiFlags = {
      ..._state.uiFlags,
      ...data,
    };
  },

  [types.SET_RESOLUTION_MOTIVES]: MutationHelpers.set,
  [types.ADD_RESOLUTION_MOTIVE]: MutationHelpers.create,
  [types.EDIT_RESOLUTION_MOTIVE]: MutationHelpers.update,
  [types.DELETE_RESOLUTION_MOTIVE]: MutationHelpers.destroy,
};

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations,
};
