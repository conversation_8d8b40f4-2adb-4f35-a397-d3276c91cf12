<template>
  <div class="flex-1 overflow-auto p-4">
    <woot-button
      color-scheme="success"
      class-names="button--fixed-top"
      icon="add-circle"
      @click="openAddPopup"
    >
      {{ $t('RESOLUTION_MOTIVES.HEADER_BTN_TXT') }}
    </woot-button>
    <div class="flex flex-row gap-4">
      <div class="w-[60%]">
        <p
          v-if="!uiFlags.isFetching && !records.length"
          class="flex h-full items-center flex-col justify-center"
        >
          {{ $t('RESOLUTION_MOTIVES.LIST.404') }}
        </p>
        <woot-loading-state
          v-if="uiFlags.isFetching"
          :message="$t('RESOLUTION_MOTIVES.LOADING')"
        />
        <table v-if="!uiFlags.isFetching && records.length" class="woot-table">
          <thead>
            <th
              v-for="thHeader in $t('RESOLUTION_MOTIVES.LIST.TABLE_HEADER')"
              :key="thHeader"
            >
              {{ thHeader }}
            </th>
          </thead>
          <tbody>
            <tr v-for="(motive, index) in records" :key="motive.id">
              <td class="motive-name">
                <span class="overflow-hidden whitespace-nowrap text-ellipsis">{{
                  motive.name
                }}</span>
              </td>
              <td>{{ motive.description || '-' }}</td>
              <td>
                <span
                  :class="[
                    'badge',
                    motive.active ? 'badge--success' : 'badge--warning'
                  ]"
                >
                  {{ motive.active ? $t('RESOLUTION_MOTIVES.ACTIVE') : $t('RESOLUTION_MOTIVES.INACTIVE') }}
                </span>
              </td>
              <td class="button-wrapper">
                <woot-button
                  v-tooltip.top="$t('RESOLUTION_MOTIVES.FORM.EDIT')"
                  variant="smooth"
                  size="tiny"
                  color-scheme="secondary"
                  class-names="grey-btn"
                  :is-loading="loading[motive.id]"
                  icon="edit"
                  @click="openEditPopup(motive)"
                />
                <woot-button
                  v-tooltip.top="$t('RESOLUTION_MOTIVES.FORM.DELETE')"
                  variant="smooth"
                  color-scheme="alert"
                  size="tiny"
                  icon="dismiss-circle"
                  class-names="grey-btn"
                  :is-loading="loading[motive.id]"
                  :disabled="!motive.can_be_deleted"
                  @click="openDeletePopup(motive, index)"
                />
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <div class="w-[34%]">
        <span v-dompurify-html="$t('RESOLUTION_MOTIVES.SIDEBAR_TXT')" />
      </div>
    </div>
    <woot-modal :show.sync="showAddPopup" :on-close="hideAddPopup">
      <add-resolution-motive @close="hideAddPopup" />
    </woot-modal>

    <woot-modal :show.sync="showEditPopup" :on-close="hideEditPopup">
      <edit-resolution-motive
        :selected-response="selectedResponse"
        @close="hideEditPopup"
      />
    </woot-modal>

    <woot-delete-modal
      :show.sync="showDeleteConfirmationPopup"
      :on-close="closeDeletePopup"
      :on-confirm="confirmDeletion"
      :title="$t('RESOLUTION_MOTIVES.DELETE.CONFIRM.TITLE')"
      :message="$t('RESOLUTION_MOTIVES.DELETE.CONFIRM.MESSAGE')"
      :message-value="deleteMessage"
      :confirm-text="deleteConfirmText"
      :reject-text="deleteRejectText"
    />
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import AddResolutionMotive from './AddResolutionMotive.vue';
import EditResolutionMotive from './EditResolutionMotive.vue';
import alertMixin from 'shared/mixins/alertMixin';

export default {
  components: {
    AddResolutionMotive,
    EditResolutionMotive,
  },
  mixins: [alertMixin],
  data() {
    return {
      loading: {},
      showAddPopup: false,
      showEditPopup: false,
      showDeleteConfirmationPopup: false,
      selectedResponse: {},
      resolutionMotiveAPI: {
        message: '',
      },
      deleteMessage: '',
      deleteConfirmText: '',
      deleteRejectText: '',
    };
  },
  computed: {
    ...mapGetters({
      records: 'resolutionMotives/getResolutionMotives',
      uiFlags: 'resolutionMotives/getUIFlags',
    }),
  },
  mounted() {
    this.$store.dispatch('resolutionMotives/get');
  },
  methods: {
    openAddPopup() {
      this.showAddPopup = true;
    },
    hideAddPopup() {
      this.showAddPopup = false;
    },
    openEditPopup(response) {
      this.showEditPopup = true;
      this.selectedResponse = response;
    },
    hideEditPopup() {
      this.showEditPopup = false;
    },
    openDeletePopup(response) {
      this.showDeleteConfirmationPopup = true;
      this.selectedResponse = response;
      this.deleteMessage = ` ${response.name}?`;
      this.deleteConfirmText = this.$t('RESOLUTION_MOTIVES.DELETE.CONFIRM.YES');
      this.deleteRejectText = this.$t('RESOLUTION_MOTIVES.DELETE.CONFIRM.NO');
    },
    closeDeletePopup() {
      this.showDeleteConfirmationPopup = false;
    },
    confirmDeletion() {
      this.loading[this.selectedResponse.id] = true;
      this.closeDeletePopup();
      this.deleteResolutionMotive(this.selectedResponse.id);
    },
    async deleteResolutionMotive(id) {
      try {
        await this.$store.dispatch('resolutionMotives/delete', id);
        this.showAlert(this.$t('RESOLUTION_MOTIVES.DELETE.API.SUCCESS_MESSAGE'));
      } catch (error) {
        const errorMessage = error?.response?.data?.error || this.$t('RESOLUTION_MOTIVES.DELETE.API.ERROR_MESSAGE');
        this.showAlert(errorMessage);
      } finally {
        this.loading[id] = false;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.motive-name {
  max-width: 12rem;
}

.badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  
  &--success {
    @apply bg-green-100 text-green-800;
  }
  
  &--warning {
    @apply bg-yellow-100 text-yellow-800;
  }
}
</style>
