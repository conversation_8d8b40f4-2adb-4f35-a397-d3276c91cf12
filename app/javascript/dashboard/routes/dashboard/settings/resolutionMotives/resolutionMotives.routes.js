import { frontendURL } from '../../../../helper/URLHelper';

const SettingsContent = () => import('../Wrapper.vue');
const Index = () => import('./Index.vue');

export default {
  routes: [
    {
      path: frontendURL('accounts/:accountId/settings/resolution-motives'),
      component: SettingsContent,
      props: {
        headerTitle: 'RESOLUTION_MOTIVES.HEADER',
        icon: 'checkmark-circle',
        showNewButton: false,
      },
      children: [
        {
          path: '',
          name: 'resolution_motives_wrapper',
          roles: ['administrator'],
          redirect: 'list',
        },
        {
          path: 'list',
          name: 'resolution_motives_list',
          roles: ['administrator'],
          component: Index,
        },
      ],
    },
  ],
};
