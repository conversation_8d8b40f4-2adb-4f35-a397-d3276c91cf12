<template>
  <div class="flex flex-col h-auto overflow-auto">
    <woot-modal-header
      :header-title="$t('RESOLUTION_MOTIVES.ADD.TITLE')"
      :header-content="$t('RESOLUTION_MOTIVES.ADD.DESC')"
    />
    <form class="flex flex-col w-full" @submit.prevent="addResolutionMotive">
      <div class="w-full">
        <woot-input
          v-model="name"
          :label="$t('RESOLUTION_MOTIVES.FORM.NAME.LABEL')"
          type="text"
          :class="{ error: $v.name.$error }"
          :error="nameError"
          :placeholder="$t('RESOLUTION_MOTIVES.FORM.NAME.PLACEHOLDER')"
          @blur="$v.name.$touch"
        />
        <woot-input
          v-model="description"
          :label="$t('RESOLUTION_MOTIVES.FORM.DESCRIPTION.LABEL')"
          type="textarea"
          :class="{ error: $v.description.$error }"
          :error="descriptionError"
          :placeholder="$t('RESOLUTION_MOTIVES.FORM.DESCRIPTION.PLACEHOLDER')"
          @blur="$v.description.$touch"
        />
        <div class="flex items-center gap-2 mt-4">
          <input
            id="active"
            v-model="active"
            type="checkbox"
            class="rounded"
          />
          <label for="active" class="text-sm">
            {{ $t('RESOLUTION_MOTIVES.FORM.ACTIVE.LABEL') }}
          </label>
        </div>
      </div>
      <div class="flex flex-row justify-end gap-2 px-0 py-2">
        <woot-button class="button clear" @click.prevent="onClose">
          {{ $t('RESOLUTION_MOTIVES.FORM.CANCEL') }}
        </woot-button>
        <woot-button type="submit" :is-loading="isCreating">
          {{ $t('RESOLUTION_MOTIVES.FORM.CREATE') }}
        </woot-button>
      </div>
    </form>
  </div>
</template>

<script>
import { required, minLength, maxLength } from 'vuelidate/lib/validators';
import { mapGetters } from 'vuex';
import alertMixin from 'shared/mixins/alertMixin';

export default {
  mixins: [alertMixin],
  data() {
    return {
      name: '',
      description: '',
      active: true,
    };
  },
  validations: {
    name: {
      required,
      minLength: minLength(1),
      maxLength: maxLength(100),
    },
    description: {
      maxLength: maxLength(500),
    },
  },
  computed: {
    ...mapGetters({
      isCreating: 'resolutionMotives/getUIFlags',
    }),
    nameError() {
      if (this.$v.name.$error) {
        if (!this.$v.name.required) {
          return this.$t('RESOLUTION_MOTIVES.FORM.NAME.REQUIRED_ERROR');
        }
        if (!this.$v.name.minLength) {
          return this.$t('RESOLUTION_MOTIVES.FORM.NAME.MINIMUM_LENGTH_ERROR');
        }
        if (!this.$v.name.maxLength) {
          return this.$t('RESOLUTION_MOTIVES.FORM.NAME.MAXIMUM_LENGTH_ERROR');
        }
      }
      return '';
    },
    descriptionError() {
      if (this.$v.description.$error) {
        if (!this.$v.description.maxLength) {
          return this.$t('RESOLUTION_MOTIVES.FORM.DESCRIPTION.MAXIMUM_LENGTH_ERROR');
        }
      }
      return '';
    },
  },
  methods: {
    onClose() {
      this.$emit('close');
    },
    async addResolutionMotive() {
      this.$v.$touch();
      if (this.$v.$invalid) {
        return;
      }

      try {
        await this.$store.dispatch('resolutionMotives/create', {
          name: this.name,
          description: this.description,
          active: this.active,
        });
        this.showAlert(this.$t('RESOLUTION_MOTIVES.ADD.API.SUCCESS_MESSAGE'));
        this.onClose();
      } catch (error) {
        const errorMessage = error?.response?.data?.message || this.$t('RESOLUTION_MOTIVES.ADD.API.ERROR_MESSAGE');
        this.showAlert(errorMessage);
      }
    },
  },
};
</script>
