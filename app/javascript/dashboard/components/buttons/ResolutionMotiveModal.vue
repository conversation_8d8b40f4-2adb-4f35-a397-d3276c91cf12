<template>
  <div class="flex flex-col h-auto overflow-auto">
    <woot-modal-header
      :header-title="$t('CONVERSATION.RESOLUTION_MODAL.TITLE')"
      :header-content="$t('CONVERSATION.RESOLUTION_MODAL.DESC')"
    />
    <form class="flex flex-col w-full" @submit.prevent="resolveConversation">
      <div class="w-full">
        <label class="block text-sm font-medium text-slate-700 mb-2">
          {{ $t('CONVERSATION.RESOLUTION_MODAL.MOTIVE_LABEL') }}
        </label>
        <select
          v-model="selectedMotiveId"
          class="w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          :class="{ 'border-red-500': showError }"
        >
          <option value="">
            {{ $t('CONVERSATION.RESOLUTION_MODAL.SELECT_MOTIVE') }}
          </option>
          <option
            v-for="motive in activeMotives"
            :key="motive.id"
            :value="motive.id"
          >
            {{ motive.name }}
          </option>
        </select>
        <p v-if="showError" class="mt-1 text-sm text-red-600">
          {{ $t('CONVERSATION.RESOLUTION_MODAL.MOTIVE_REQUIRED') }}
        </p>
        <p v-if="selectedMotive && selectedMotive.description" class="mt-2 text-sm text-slate-600">
          {{ selectedMotive.description }}
        </p>
      </div>
      
      <div class="flex items-center gap-2 mt-4">
        <input
          id="skipMotive"
          v-model="skipMotive"
          type="checkbox"
          class="rounded"
        />
        <label for="skipMotive" class="text-sm text-slate-600">
          {{ $t('CONVERSATION.RESOLUTION_MODAL.SKIP_MOTIVE') }}
        </label>
      </div>

      <div class="flex flex-row justify-end gap-2 px-0 py-4">
        <woot-button class="button clear" @click.prevent="onClose">
          {{ $t('CONVERSATION.RESOLUTION_MODAL.CANCEL') }}
        </woot-button>
        <woot-button type="submit" :is-loading="isResolving">
          {{ $t('CONVERSATION.RESOLUTION_MODAL.RESOLVE') }}
        </woot-button>
      </div>
    </form>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import alertMixin from 'shared/mixins/alertMixin';

export default {
  mixins: [alertMixin],
  props: {
    conversationId: {
      type: [String, Number],
      required: true,
    },
  },
  data() {
    return {
      selectedMotiveId: '',
      skipMotive: false,
      showError: false,
      isResolving: false,
    };
  },
  computed: {
    ...mapGetters({
      activeMotives: 'resolutionMotives/getActiveResolutionMotives',
    }),
    selectedMotive() {
      return this.activeMotives.find(motive => motive.id === this.selectedMotiveId);
    },
  },
  mounted() {
    // Load resolution motives if not already loaded
    if (this.activeMotives.length === 0) {
      this.$store.dispatch('resolutionMotives/get');
    }
  },
  methods: {
    onClose() {
      this.$emit('close');
    },
    async resolveConversation() {
      // Validate motive selection if not skipping
      if (!this.skipMotive && !this.selectedMotiveId) {
        this.showError = true;
        return;
      }
      
      this.showError = false;
      this.isResolving = true;

      try {
        const payload = {
          conversationId: this.conversationId,
          status: 'resolved',
        };

        // Add resolution motive if selected
        if (this.selectedMotiveId && !this.skipMotive) {
          payload.resolution_motive_id = this.selectedMotiveId;
        }

        await this.$store.dispatch('toggleStatus', payload);
        
        this.showAlert(this.$t('CONVERSATION.CHANGE_STATUS'));
        this.onClose();
      } catch (error) {
        this.showAlert(this.$t('CONVERSATION.RESOLUTION_MODAL.ERROR'));
      } finally {
        this.isResolving = false;
      }
    },
  },
};
</script>
