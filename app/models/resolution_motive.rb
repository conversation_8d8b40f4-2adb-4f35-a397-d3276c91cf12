class ResolutionMotive < ApplicationRecord
  belongs_to :account
  has_many :conversations, dependent: :nullify

  validates :name, presence: true, length: { maximum: 100 }
  validates :name, uniqueness: { scope: :account_id, case_sensitive: false }
  validates :description, length: { maximum: 500 }
  validates :position, presence: true, numericality: { greater_than_or_equal_to: 0 }

  scope :active, -> { where(active: true) }
  scope :ordered, -> { order(:position, :name) }
  scope :for_account, ->(account) { where(account: account) }

  before_validation :set_default_position, on: :create

  def display_name
    name
  end

  def can_be_deleted?
    conversations.count.zero?
  end

  private

  def set_default_position
    return if position.present?

    max_position = account.resolution_motives.maximum(:position) || 0
    self.position = max_position + 1
  end
end
