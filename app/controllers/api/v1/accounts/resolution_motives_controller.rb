class Api::V1::Accounts::ResolutionMotivesController < Api::V1::Accounts::BaseController
  before_action :current_account
  before_action :check_authorization
  before_action :fetch_resolution_motive, except: [:index, :create]

  def index
    @resolution_motives = Current.account.resolution_motives.active.ordered
  end

  def show; end

  def create
    @resolution_motive = Current.account.resolution_motives.create!(permitted_params)
  end

  def update
    @resolution_motive.update!(permitted_params)
  end

  def destroy
    if @resolution_motive.can_be_deleted?
      @resolution_motive.destroy!
      head :ok
    else
      render json: { 
        error: 'Cannot delete resolution motive that is being used by conversations' 
      }, status: :unprocessable_entity
    end
  end

  private

  def fetch_resolution_motive
    @resolution_motive = Current.account.resolution_motives.find(params[:id])
  end

  def permitted_params
    params.require(:resolution_motive).permit(:name, :description, :active, :position)
  end

  def check_authorization
    authorize(ResolutionMotive)
  end
end
