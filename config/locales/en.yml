# Files in the config/locales directory are used for internationalization
# and are automatically loaded by Rails. If you want to use locales other
# than English, add the necessary files in this directory.
#
# To use the locales, use `I18n.t`:
#
#     I18n.t 'hello'
#
# In views, this is aliased to just `t`:
#
#     <%= t('hello') %>
#
# To use a different locale, set it with `I18n.locale`:
#
#     I18n.locale = :es
#
# This would use the information in config/locales/es.yml.
#
# The following keys must be escaped otherwise they will not be retrieved by
# the default I18n backend:
#
# true, false, on, off, yes, no
#
# Instead, surround them with single quotes.
#
# en:
#   'true': 'foo'
#
# To learn more, please read the Rails Internationalization guide
# available at https://guides.rubyonrails.org/i18n.html.

en:
  hello: "Hello world"
  messages:
    reset_password_success: Woot! Request for password reset is successful. Check your mail for instructions.
    reset_password_failure: Uh ho! We could not find any user with the specified email.
    inbox_deletetion_response: Your inbox deletion request will be processed in some time.
  administrate:
    actions:
      new_resource: New Resource
    search:
      label: Search
  errors:
    validations:
      presence: must not be blank
    webhook:
      invalid: Invalid events
    signup:
      disposable_email: We do not allow disposable emails
      invalid_email: You have entered an invalid email
      email_already_exists: "You have already signed up for an account with %{email}"
      failed: Signup failed
    data_import:
      data_type:
        invalid: Invalid data type
    contacts:
      import:
        failed: File is blank
      export:
        success: We will notify you once contacts export file is ready to view.
      email:
        invalid: Invalid email
      phone_number:
        invalid: should be in e164 format
    categories:
      locale:
        unique: should be unique in the category and portal
    dyte:
      invalid_message_type: "Invalid message type. Action not permitted"
    slack:
      invalid_channel_id: "Invalid slack channel. Please try again"
    inboxes:
      imap:
        socket_error: Please check the network connection, IMAP address and try again.
        no_response_error: Please check the IMAP credentials and try again.
        host_unreachable_error: Host unreachable, Please check the IMAP address, IMAP port and try again.
        connection_timed_out_error: Connection timed out for %{address}:%{port}
        connection_closed_error: Connection closed.
      validations:
        name: should not start or end with symbols, and it should not have < > / \ @ characters.
    custom_filters:
      number_of_records: Limit reached. The maximum number of allowed custom filters for a user per account is 50.

  reports:
    period: Reporting period %{since} to %{until}
    utc_warning: The report generated is in UTC timezone
    agent_csv:
      agent_name: Agent name
      conversations_count: Conversations count
      avg_first_response_time: Avg first response time (Minutes)
      avg_resolution_time: Avg resolution time (Minutes)
    inbox_csv:
      inbox_name: Inbox name
      inbox_type: Inbox type
      conversations_count: No. of conversations
      avg_first_response_time: Avg first response time (Minutes)
      avg_resolution_time: Avg resolution time (Minutes)
    label_csv:
      label_title: Label
      conversations_count: No. of conversations
      avg_first_response_time: Avg first response time (Minutes)
      avg_resolution_time: Avg resolution time (Minutes)
    team_csv:
      team_name: Team name
      conversations_count: Conversations count
      avg_first_response_time: Avg first response time (Minutes)
      avg_resolution_time: Avg resolution time (Minutes)
    conversation_traffic_csv:
      timezone: Timezone
    default_group_by: day
    csat:
      headers:
        contact_name: Contact Name
        contact_email_address: Contact Email Address
        contact_phone_number: Contact Phone Number
        link_to_the_conversation: Link to the conversation
        agent_name: Agent Name
        rating: Rating
        feedback: Feedback Comment
        recorded_at: Recorded date

  notifications:
    notification_title:
      conversation_creation: "[New conversation] - #%{display_id} has been created in %{inbox_name}"
      conversation_assignment: "[Assigned to you] - #%{display_id} has been assigned to you"
      assigned_conversation_new_message: "[New message] - #%{display_id} %{content}"
      conversation_mention: "You have been mentioned in conversation [ID - %{display_id}] by %{name}"
  conversations:
    messages:
      instagram_story_content: "%{story_sender} mentioned you in the story: "
      instagram_deleted_story_content: This story is no longer available.
      deleted: This message was deleted
      delivery_status:
        error_code: "Error code: %{error_code}"
    activity:
      status:
        resolved: "Conversation was marked resolved by %{user_name}"
        contact_resolved: "Conversation was resolved by %{contact_name}"
        open: "Conversation was reopened by %{user_name}"
        pending: "Conversation was marked as pending by %{user_name}"
        snoozed: "Conversation was snoozed by %{user_name}"
        auto_resolved: "Conversation was marked resolved by system due to %{duration} days of inactivity"
        system_auto_open: System reopened the conversation due to a new incoming message.
      priority:
        added: '%{user_name} set the priority to %{new_priority}'
        updated: '%{user_name} changed the priority from %{old_priority} to %{new_priority}'
        removed: '%{user_name} removed the priority'
      assignee:
        self_assigned: "%{user_name} self-assigned this conversation"
        assigned: "Assigned to %{assignee_name} by %{user_name}"
        removed: "Conversation unassigned by %{user_name}"
      team:
        assigned: "Assigned to %{team_name} by %{user_name}"
        assigned_with_assignee: "Assigned to %{assignee_name} via %{team_name} by %{user_name}"
        removed: "Unassigned from %{team_name} by %{user_name}"
      labels:
        added: "%{user_name} added %{labels}"
        removed: "%{user_name} removed %{labels}"
      muted: "%{user_name} has muted the conversation"
      unmuted: "%{user_name} has unmuted the conversation"
    templates:
      greeting_message_body: "%{account_name} typically replies in a few hours."
      ways_to_reach_you_message_body: "Give the team a way to reach you."
      email_input_box_message_body: "Get notified by email"
      csat_input_message_body: "Please rate the conversation"
    resolution_note: "Conversation resolved by %{user} with motive: %{motive}"
    reply:
      email:
        header:
          from_with_name: "%{assignee_name} from %{inbox_name} <%{from_email}>"
          reply_with_name: "%{assignee_name} from %{inbox_name} <reply+%{reply_email}>"
          friendly_name: "%{sender_name} from %{business_name} <%{from_email}>"
          professional_name: "%{business_name} <%{from_email}>"
      channel_email:
        header:
          reply_with_name: "%{assignee_name} from %{inbox_name} <%{from_email}>"
          reply_with_inbox_name: "%{inbox_name} <%{from_email}>"
      email_subject: "New messages on this conversation"
      transcript_subject: "Conversation Transcript"
    survey:
      response: "Please rate this conversation, %{link}"
  contacts:
    online:
      delete: "%{contact_name} is Online, please try again later"
  integration_apps:
    dyte:
      name: "Dyte"
      description: "Dyte is tool that helps you to add live audio & video to your application with just a few lines of code. This integration allows you to give an option to your agents to have a video or voice call with your customers from without leaving Chatwoot."
      meeting_name: "%{agent_name} has started a meeting"
    slack:
      name: "Slack"
      description: "Slack is a chat tool that brings all your communication together in one place. By integrating Slack, you can get notified of all the new conversations in your account right inside your Slack."
    webhooks:
      name: "Webhooks"
      description: "Webhook events provide you the realtime information about what's happening in your account. You can make use of the webhooks to communicate the events to your favourite apps like Slack or Github. Click on Configure to set up your webhooks."
    dialogflow:
      name: "Dialogflow"
      description: "Build chatbots using Dialogflow and connect them to your inbox quickly. Let the bots handle the queries before handing them off to a customer service agent."
    fullcontact:
      name: "Fullcontact"
      description: "FullContact integration helps to enrich visitor profiles. Identify the users as soon as they share their email address and offer them tailored customer service. Connect your FullContact to your account by sharing the FullContact API Key."
    google_translate:
      name: "Google Translate"
      description: "Make it easier for agents to translate messages by adding a Google Translate Integration. Google translate helps to identify the language automatically and convert it to the language chosen by the agent/account admin."
    openai:
      name: "OpenAI"
      description: "Integrate powerful AI features into Chatwoot by leveraging the GPT models from OpenAI."
  public_portal:
    search:
      search_placeholder: Search for article by title or body...
      empty_placeholder: No results found.
      loading_placeholder: Searching...
      results_title: Search results
    toc_header: 'On this page'
    hero:
      sub_title: Search for the articles here or browse the categories below.
    common:
      home: Home
      last_updated_on: Last updated on %{last_updated_on}
      view_all_articles: View all
      article: article
      articles: articles
      author: author
      authors: authors
      other: other
      others: others
      by: By
      no_articles: There are no articles here
    footer:
      made_with: Made with
    header:
      go_to_homepage: Website
      appearance:
        system: System
        light: Light
        dark: Dark
      featured_articles: Featured Articles
      uncategorized: Uncategorized
    404:
      title: Page not found
      description: We couldn't find the page you were looking for.
      back_to_home: Go to home page
  slack_unfurl:
    fields:
     name: Name
     email: Email
     phone_number: Phone
     company_name: Company
     inbox_name: Inbox
     inbox_type: Inbox Type
    button: Open conversation
