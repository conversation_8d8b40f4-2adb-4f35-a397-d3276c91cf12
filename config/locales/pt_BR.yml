#Files in the config/locales directory are used for internationalization
#and are automatically loaded by Rails. If you want to use locales other
#than English, add the necessary files in this directory.
#To use the locales, use `I18n.t`:
#I18n.t 'hello'
#In views, this is aliased to just `t`:
#<%= t('hello') %>
#To use a different locale, set it with `I18n.locale`:
#I18n.locale = :es
#This would use the information in config/locales/es.yml.
#The following keys must be escaped otherwise they will not be retrieved by
#the default I18n backend:
#true, false, on, off, yes, no
#Instead, surround them with single quotes.
#en:
#'true': 'foo'
#To learn more, please read the Rails Internationalization guide
#available at https://guides.rubyonrails.org/i18n.html.
pt_BR:
  hello: 'Olá, mundo'
  messages:
    reset_password_success: Legal! A solicitação de alteração de senha foi bem sucedida. Verifique seu e-mail para obter instruções.
    reset_password_failure: Uh ho! Não conseguimos encontrar nenhum usuário com o e-mail especificado.
    inbox_deletetion_response: Seu pedido de exclusão do canal de atendimento será processado dentro de algum tempo.
  administrate:
    actions:
      new_resource: Novo recurso
      show_resource: Visualizar %{name}
      edit_resource: Editar %{name}
      edit: Editar
      destroy: Deletar
      confirm: Confirmar
    search:
      label: Pesquisar
    controller:
      update:
        success: Atualizado com sucesso!
  errors:
    validations:
      presence: não pode ficar em branco
    webhook:
      invalid: Eventos inválidos
    signup:
      disposable_email: Não permitimos e-mails descartáveis
      invalid_email: Você digitou um email inválido
      email_already_exists: 'Você já se cadastrou para uma conta com %{email}'
      failed: Registro falhou
    data_import:
      data_type:
        invalid: Tipo de dado inválido
    contacts:
      import:
        failed: Arquivo vazio
      export:
        success: Avisaremos você assim que a exportação de arquivos estiver pronta para ser exibida.
      email:
        invalid: E-mail inválido
      phone_number:
        invalid: deve estar no formato e164
    categories:
      locale:
        unique: deve ser único na categoria e no portal
    dyte:
      invalid_message_type: 'Tipo de mensagem inválido. Ação não permitida'
    slack:
      invalid_channel_id: 'Canal de slack inválido. Por favor, tente novamente'
    inboxes:
      imap:
        socket_error: Por favor, verifique a conexão de rede, endereço IMAP e tente novamente.
        no_response_error: Por favor, verifique as credenciais de IMAP e tente novamente.
        host_unreachable_error: Servidor inacessível, por favor, verifique o endereço e a porta de IMAP e tente novamente.
        connection_timed_out_error: Tempo esgotado de conexão para %{address}:%{port}
        connection_closed_error: Conexão fechada.
      validations:
        name: 'não deve iniciar ou terminar com símbolos e não deve ter os caracteres: < > / \ @.'
    custom_filters:
      number_of_records: Limite atingido. O número máximo de filtros personalizados permitidos para um usuário por conta é de 50.
  reports:
    period: Reportando o período %{since} a %{until}
    utc_warning: O relatório gerado está em fuso horário UTC
    agent_csv:
      agent_name: Nome do Agente
      conversations_count: Contagem de conversas
      first_conversations_count: Contagem de primeiras conversas de novos contatos
      avg_first_response_time: Tempo médio de primeira resposta (minutos)
      avg_resolution_time: Tempo médio de resolução (minutos)
    inbox_csv:
      inbox_name: Nome do Canal de Atendimento
      inbox_type: Tipo do Canal de Atendimento
      conversations_count: Nº de Conversas
      first_conversations_count: Nº de primeira conversa de novos contatos
      avg_first_response_time: Média de tempo da primeira resposta (minutos)
      avg_resolution_time: Tempo médio de resolução (minutos)
    label_csv:
      label_title: Nome do campo
      conversations_count: Nº de Conversas
      first_conversations_count: Nº de primeira conversa de novos contatos
      avg_first_response_time: Tempo médio de primeira resposta (minutos)
      avg_resolution_time: Tempo médio de resolução (minutos)
    team_csv:
      team_name: Nome do departamento
      conversations_count: Contagem de conversas
      first_conversations_count: Contagem de primeiras conversas de novos contatos
      avg_first_response_time: Tempo médio de primeira resposta (minutos)
      avg_resolution_time: Tempo médio de resolução (minutos)
    conversation_traffic_csv:
      timezone: Timezone
    default_group_by: dia
    csat:
      headers:
        contact_name: Nome do contato
        contact_email_address: E-mail de contato
        contact_phone_number: Telefone de contato
        link_to_the_conversation: Link para a conversa
        agent_name: Nome do Agente
        rating: Classificação
        feedback: Comentário de Feedback
        recorded_at: Data de gravação
  notifications:
    notification_title:
      conversation_creation: '[Nova conversa] - #%{display_id} foi criada em %{inbox_name}'
      conversation_assignment: '[Atribuído a você] - #%{display_id} foi atribuída a você'
      assigned_conversation_new_message: '[Nova mensagem] - #%{display_id} %{content}'
      conversation_mention: 'Você foi mencionado na conversa [ID - %{display_id}] por %{name}'
  conversations:
    messages:
      instagram_story_content: '%{story_sender} mencionou você na conversa: '
      instagram_deleted_story_content: Este Story não está mais disponível.
      deleted: Esta mensagem foi apagada
      delivery_status:
        error_code: 'Código de erro: %{error_code}'
    activity:
      status:
        resolved: 'Conversa foi marcada como resolvida por %{user_name}'
        contact_resolved: 'A conversa foi resolvida por %{contact_name}'
        open: 'Conversa foi reaberta por %{user_name}'
        pending: 'Conversa foi marcada como pendente por %{user_name}'
        snoozed: 'Conversa não atribuída por %{user_name}'
        auto_resolved: 'Conversa foi marcada como resolvida pelo sistema por ter %{duration} dias de inatividade'
        system_auto_open: O sistema reabriu a conversa devido a uma nova mensagem recebida.
      priority:
        added: '%{user_name} definiu a prioridade para %{new_priority}'
        updated: '%{user_name} mudou a prioridade de %{old_priority} para %{new_priority}'
        removed: '%{user_name} removeu a prioridade'
      assignee:
        self_assigned: '%{user_name} atribuiu a si mesmo essa conversa'
        assigned: 'Atribuído a %{assignee_name} por %{user_name}'
        removed: 'Conversa não atribuída por %{user_name}'
      team:
        assigned: 'Atribuído a %{team_name} por %{user_name}'
        assigned_with_assignee: 'Atribuído a %{assignee_name} via %{team_name} por %{user_name}'
        removed: 'Desatribuído de %{team_name} por %{user_name}'
      labels:
        added: '%{user_name} adicionou %{labels}'
        removed: '%{user_name} removeu %{labels}'
      muted: '%{user_name} silenciou a conversa'
      unmuted: '%{user_name} reativou a conversa'
    templates:
      greeting_message_body: '%{account_name} normalmente responde em algumas horas.'
      ways_to_reach_you_message_body: 'Informe uma forma para entrarmos em contato com você.'
      email_input_box_message_body: 'Seja notificado por e-mail'
      csat_input_message_body: 'Por favor, classifique a conversa'
    resolution_note: 'Conversa resolvida por %{user} com motivo: %{motive}'
    reply:
      email:
        header:
          from_with_name: '%{assignee_name} de %{inbox_name} <reply+%{from_email}>'
          reply_with_name: '%{assignee_name} de %{inbox_name} <reply+%{reply_email}>'
          friendly_name: '%{sender_name} de %{business_name} <reply+%{from_email}>'
          professional_name: '%{business_name} <%{from_email}>'
      channel_email:
        header:
          reply_with_name: '%{assignee_name} de %{inbox_name} <reply+%{from_email}>'
          reply_with_inbox_name: '%{inbox_name} <%{from_email}>'
      email_subject: 'Novas mensagens nesta conversa'
      transcript_subject: 'Transcrição da conversa'
    survey:
      response: 'Por favor, classifique esta conversa, %{link}'
  contacts:
    online:
      delete: '%{contact_name} está Online, por favor, tente novamente mais tarde'
  integration_apps:
    dyte:
      name: 'Dyte'
      description: 'Dyte é uma ferramenta que ajuda você a adicionar áudio e vídeo ao vivo em sua aplicação com apenas algumas linhas de código. Essa integração permite dar uma opção aos seus agentes para ter uma chamada de vídeo ou voz com os seus clientes sem sair do Omnigo.'
      meeting_name: '%{agent_name} começou a reunião'
    slack:
      name: 'Slack'
      description: 'Slack é uma ferramenta que reúne todas as suas comunicações em um só lugar. Ao integrar o Slack, você pode ser notificado de todas as novas conversas da sua conta diretamente no seu Slack.'
    webhooks:
      name: 'Webhooks'
      description: 'Webhooks fornecem informações em tempo real sobre o que está acontecendo em sua conta. Você pode usar os webhooks para comunicar eventos com seus aplicativos favoritos como Slack ou Github. Clique em Configurar para configurar seus webhooks.'
    dialogflow:
      name: 'Dialogflow'
      description: 'Construa chatbots usando Dialogflow e conecte-os ao seu canal de atendimento rapidamente. Deixe os bots lidarem com as consultas antes de entregá-los a um agente de atendimento ao cliente.'
    fullcontact:
      name: 'Contato completo'
      description: 'A integração de contatos completos ajuda a enriquecer perfis de visitantes. Identifique os usuários assim que eles compartilham seu endereço de e-mail e ofereça um atendimento ao cliente personalizado. Conecte seu FullContact à sua conta compartilhando a chave de API de Contato Fullcot.'
    google_translate:
      name: 'Google Tradutor'
      description: 'Torne isso mais fácil para agentes traduzirem mensagens adicionando uma integração com o Tradutor do Google. O Tradutor do Google ajuda a identificar automaticamente o idioma e convertê-lo para o idioma escolhido pelo administrador de conta/agente.'
    openai:
      name: 'OpenAI'
      description: 'Integre os poderosos recursos de IA ao Omnigo aproveitando os modelos GPT da OpenAI.'
  public_portal:
    search:
      search_placeholder: Pesquisar por artigo por título ou corpo...
      empty_placeholder: Nenhum resultado encontrado.
      loading_placeholder: Procurando...
      results_title: Resultados de pesquisa
    toc_header: 'Nesta página'
    hero:
      sub_title: Pesquise os artigos aqui ou navegue pelas categorias abaixo.
    common:
      home: Principal
      last_updated_on: Última atualização em %{last_updated_on}
      view_all_articles: Ver todos
      article: artigo
      articles: artigos
      author: autor
      authors: autores
      other: outro
      others: outros
      by: Por
      no_articles: Nenhum artigo aqui
    footer:
      made_with: Criado pela
    header:
      go_to_homepage: Ir para o site principal
      appearance:
        system: System
        light: Light
        dark: Dark
      featured_articles: Artigos em Destaque
      uncategorized: Sem Categoria
  slack_unfurl:
    fields:
      name: Nome
      email: e-mail
      phone_number: Telefone
      company_name: Empresa
      inbox_name: Caixa de Entrada
      inbox_type: Tipo de Caixa de Entrada
    button: Abrir conversa
