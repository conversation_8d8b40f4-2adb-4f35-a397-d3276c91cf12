# Ruby CircleCI 2.0 configuration file
#
# Check https://circleci.com/docs/2.0/language-ruby/ for more details
#
version: 2
defaults: &defaults
  working_directory: ~/build
  docker:
    # specify the version you desire here
    - image: cimg/ruby:3.2.2-browsers

    # Specify service dependencies here if necessary
    # CircleCI maintains a library of pre-built images
    # documented at https://circleci.com/docs/2.0/circleci-images/
    - image: cimg/postgres:15.3
    - image: cimg/redis:6.2.6
  environment:
    - RAILS_LOG_TO_STDOUT: false
    - COVERAGE: true
    - LOG_LEVEL: warn
  parallelism: 4
  resource_class: large

jobs:
  build:
    <<: *defaults
    steps:
      - checkout

      - run:
          name: Configure Bundler
          command: |
            echo 'export BUNDLER_VERSION=$(cat Gemfile.lock | tail -1 | tr -d " ")' >> $BASH_ENV
            source $BASH_ENV
            gem install bundler

      - run:
          name: Which bundler?
          command: bundle -v

      - run:
          name: Swap node versions
          command: |
            set +e
            wget -qO- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.1/install.sh | bash
            export NVM_DIR="$HOME/.nvm"
            [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
            [ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"
            nvm install v20
            echo 'export NVM_DIR="$HOME/.nvm"' >> $BASH_ENV
            echo '[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"' >> $BASH_ENV

      # Run bundler
      # Load installed gems from cache if possible, bundle install then save cache
      # Multiple caches are used to increase the chance of a cache hit

      - restore_cache:
          keys:
            - chatwoot-bundle-{{ .Environment.CACHE_VERSION }}-v20220524-{{ checksum "Gemfile.lock" }}

      - run: bundle install --frozen --path ~/.bundle
      - save_cache:
          paths:
            - ~/.bundle
          key: chatwoot-bundle-{{ .Environment.CACHE_VERSION }}-v20220524-{{ checksum "Gemfile.lock" }}

      # Only necessary if app uses webpacker or yarn in some other way
      - restore_cache:
          keys:
            - chatwoot-yarn-{{ .Environment.CACHE_VERSION }}-{{ checksum "yarn.lock" }}
            - chatwoot-yarn-

      - run:
          name: yarn
          command: yarn install --frozen-lockfile --cache-folder ~/.cache/yarn

      # Store yarn / webpacker cache
      - save_cache:
          key: chatwoot-yarn-{{ .Environment.CACHE_VERSION }}-{{ checksum "yarn.lock" }}
          paths:
            - ~/.cache/yarn

      - run:
          name: Download cc-test-reporter
          command: |
            mkdir -p ~/tmp
            curl -L https://codeclimate.com/downloads/test-reporter/test-reporter-latest-linux-amd64 > ~/tmp/cc-test-reporter
            chmod +x ~/tmp/cc-test-reporter
      - persist_to_workspace:
          root: ~/tmp
          paths:
            - cc-test-reporter

      # verify swagger specification
      - run:
          name: Verify swagger API specification
          command: |
            bundle exec rake swagger:build
            if [[ `git status swagger/swagger.json --porcelain` ]]
            then
              echo "ERROR: The swagger.json file is not in sync with the yaml specification. Run 'rake swagger:build' and commit 'swagger/swagger.json'."
              exit 1
            fi
            curl -L https://repo1.maven.org/maven2/org/openapitools/openapi-generator-cli/6.3.0/openapi-generator-cli-6.3.0.jar > ~/tmp/openapi-generator-cli-6.3.0.jar
            java -jar ~/tmp/openapi-generator-cli-6.3.0.jar validate -i swagger/swagger.json

      # Database setup
      - run: bundle exec rake db:create
      - run: bundle exec rake db:schema:load

      - run:
          name: Bundle audit
          command: bundle exec bundle audit update && bundle exec bundle audit check -v

      - run:
          name: Rubocop
          command: bundle exec rubocop

      # - run:
      #     name: Brakeman
      #     command: bundle exec brakeman

      - run:
          name: eslint
          command: yarn run eslint

      # Run frontend tests
      - run:
          name: Run frontend tests
          command: |
            mkdir -p ~/tmp/test-results/frontend_specs
            ~/tmp/cc-test-reporter before-build
            TESTFILES=$(circleci tests glob **/specs/*.spec.js | circleci tests split --split-by=timings)
            yarn test:coverage --profile 10 \
                               --out ~/tmp/test-results/yarn.xml \
                               -- ${TESTFILES}
      - run:
          name: Code Climate Test Coverage
          command: |
            ~/tmp/cc-test-reporter format-coverage -t lcov -o "coverage/codeclimate.frontend_$CIRCLE_NODE_INDEX.json"

      # Run rails tests
      - run:
          name: Run backend tests
          command: |
            mkdir -p ~/tmp/test-results/rspec
            mkdir -p ~/tmp/test-artifacts
            mkdir -p coverage
            ~/tmp/cc-test-reporter before-build
            TESTFILES=$(circleci tests glob "spec/**/*_spec.rb" | circleci tests split --split-by=timings)
            bundle exec rspec --format progress \
                              --format RspecJunitFormatter \
                              --out ~/tmp/test-results/rspec.xml \
                              -- ${TESTFILES}
          no_output_timeout: 30m
      - run:
          name: Code Climate Test Coverage
          command: |
            ~/tmp/cc-test-reporter format-coverage -t simplecov -o "coverage/codeclimate.$CIRCLE_NODE_INDEX.json"

      - persist_to_workspace:
          root: coverage
          paths:
            - codeclimate.*.json
      # collect reports
      - store_test_results:
          path: ~/tmp/test-results
      - store_artifacts:
          path: ~/tmp/test-artifacts
      - store_artifacts:
          path: log

  upload-coverage:
    working_directory: ~/build
    docker:
      # specify the version you desire here
      - image: circleci/ruby:3.0.2-node-browsers
    environment:
      - CC_TEST_REPORTER_ID: caf26a895e937974a90860cfadfded20891cfd1373a5aaafb3f67406ab9d433f
    steps:
      - attach_workspace:
          at: ~/build
      - run:
          name: Download cc-test-reporter
          command: |
            mkdir -p ~/tmp
            curl -L https://codeclimate.com/downloads/test-reporter/test-reporter-latest-linux-amd64 > ~/tmp/cc-test-reporter
            chmod +x ~/tmp/cc-test-reporter
      - persist_to_workspace:
          root: ~/tmp
          paths:
            - cc-test-reporter
      - run:
          name: Upload coverage results to Code Climate
          command: |
            ~/tmp/cc-test-reporter sum-coverage --output - codeclimate.*.json |  ~/tmp/cc-test-reporter upload-coverage --debug --input -

workflows:
  version: 2

  commit:
    jobs:
      - build
      - upload-coverage:
          requires:
            - build
