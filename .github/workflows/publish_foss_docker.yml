# #
# # This action will publish Chatwoot CE docker image.
# # This is set to run against merges to develop, master
# # and when tags are created.
# #

name: Publish Chatwoot CE docker images
on:
  push:
    branches:
      - develop
      - master
    tags:
      - v*
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    env:
      GIT_REF: ${{ github.head_ref || github.ref_name }} # ref_name to get tags/branches
    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v1

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v1

      - name: Strip enterprise code
        run: |
          rm -rf enterprise
          rm -rf spec/enterprise

      - name: Set Edição do OmniGO!
        run: |
          echo -en '\nENV CW_EDITION="ce"' >> docker/Dockerfile

      - name: set docker tag
        run: |
          echo "DOCKER_TAG=chatwoot/chatwoot:$GIT_REF-ce" >> $GITHUB_ENV

      - name: replace docker tag if master
        if: github.ref_name == 'master'
        run: |
          echo "DOCKER_TAG=chatwoot/chatwoot:latest-ce" >> $GITHUB_ENV

      - name: Login to DockerHub
        uses: docker/login-action@v1
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Build and push
        uses: docker/build-push-action@v2
        with:
          context: .
          file: docker/Dockerfile
          platforms: linux/amd64
          push: true
          tags: ${{ env.DOCKER_TAG }}
