# #
# # This workflow will run specs related to response bot
# # This can only be activated in installations Where vector extension is available.
# #

name: Run Response Bot spec
on:
  push:
    branches:
      - develop
      - master
  pull_request:
  workflow_dispatch:

jobs:
  test:
    runs-on: ubuntu-20.04
    services:
      postgres:
        image: ankane/pgvector
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: ""
          POSTGRES_DB: postgres
          POSTGRES_HOST_AUTH_METHOD: trust
        ports:
          - 5432:5432
        # needed because the postgres container does not provide a healthcheck
        # tmpfs makes DB faster by using RAM
        options: >-
          --mount type=tmpfs,destination=/var/lib/postgresql/data
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      redis:
        image: redis
        ports:
          - 6379:6379
        options: --entrypoint redis-server

    steps:
    - uses: actions/checkout@v3
      with:
        ref: ${{ github.event.pull_request.head.ref }}
        repository: ${{ github.event.pull_request.head.repo.full_name }}

    - uses: ruby/setup-ruby@v1
      with:
        bundler-cache: true # runs 'bundle install' and caches installed gems automatically

    - uses: actions/setup-node@v3
      with:
        node-version: 20
        cache: yarn

    - name: yarn
      run: yarn install

    - name: Create database
      run: bundle exec rake db:create

    - name: Seed database
      run: bundle exec rake db:schema:load

    - name: Enable ResponseBotService in installation
      run: RAILS_ENV=test bundle exec rails runner "Features::ResponseBotService.new.enable_in_installation"

    # Run  Response Bot specs
    - name: Run backend tests
      run: |
        bundle exec rspec \
          spec/enterprise/controllers/api/v1/accounts/response_sources_controller_spec.rb \
          spec/enterprise/services/enterprise/message_templates/response_bot_service_spec.rb \
          spec/enterprise/controllers/enterprise/api/v1/accounts/inboxes_controller_spec.rb:47 \
          --profile=10 \
          --format documentation

    - name: Upload rails log folder
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: rails-log-folder
        path: log
